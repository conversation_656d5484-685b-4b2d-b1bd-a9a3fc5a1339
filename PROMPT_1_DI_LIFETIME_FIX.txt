🚨 PROMPT 1: DEPENDENCY INJECTION LIFETIME DÜZELTMESİ 🚨

GÖREV: AutofacBusinessModule'deki SingleInstance() sorununu çöz

MEVCUT SORUN:
- Tüm DAL ve Manager sınıfları SingleInstance() olarak kayıtlı
- Bu 100,000 kullanıcıda MEMORY LEAK'e neden olacak
- Thread-safety sorunları yaratacak

YAPILACAKLAR:

🔧 ADIM 1: AutofacBusinessModule Analizi
- Business/DependencyResolvers/Autofac/AutofacBusinessModule.cs dosyasını aç
- Mevcut SingleInstance() kayıtlarını listele
- Hangi sınıfların SingleInstance() kalması gerektiğini belirle

🔧 ADIM 2: Lifetime Değişiklikleri
DEĞIŞTIRILECEKLER (.InstancePerLifetimeScope() yapılacak):
- Tüm Manager sınıfları (UserManager, MemberManager, vs.)
- Tüm DAL sınıfları (EfUserDal, EfMemberDal, vs.)

KALACAKLAR (.SingleInstance() olarak):
- ITokenHelper, JwtHelper
- ILogService, FileLoggerService, PerformanceLoggerService
- ICacheManager, MultiTenantCacheManager
- Stopwatch
- IHttpContextAccessor

🔧 ADIM 3: Değişiklikleri Uygula
- AutofacBusinessModule.cs dosyasında gerekli değişiklikleri yap
- Sadece stateless ve thread-safe sınıfları SingleInstance() bırak

🔧 ADIM 4: Build Test
- Projeyi build et
- Hata varsa analiz et ve düzelt
- Dependency injection circular dependency kontrolü yap

BEKLENEN SONUÇ:
✅ Build SUCCESS
✅ Memory leak riski %90 azalma
✅ Thread-safety sorunları çözülmüş

BAŞARI KRİTERİ:
- Build hatası yok
- Circular dependency yok
- Sadece stateless sınıflar SingleInstance()

Bu prompt tamamlandıktan sonra "✅ PROMPT 1 TAMAMLANDI - PROMPT 2'YE GEÇİLEBİLİR" yaz.

=== PROMPT 1 SONU ===

AI, yukarıdaki talimatları takip et. Sadece DI lifetime sorununu çöz, başka hiçbir şeye dokunma!
