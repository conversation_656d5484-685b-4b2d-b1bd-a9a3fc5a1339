🚨 PROMPT 2: DbContext DEPENDENCY INJECTION KURULUMU 🚨

GÖREV: GymContext'i DI container'a kaydet ve base sınıfları hazırla

MEVCUT SORUN:
- Her metodda "using (var context = new GymContext())" kullanılıyor
- DbContext creation overhead'i çok yüksek
- Connection pool tükenme riski

YAPILACAKLAR:

🔧 ADIM 1: GymContext DI Kaydı
- WebAPI/Program.cs dosyasını aç
- builder.Services.AddDbContext<GymContext>() kaydını ekle
- Connection string'i environment'a göre ayarla

EKLENECEK KOD:
```csharp
// DbContext DI kaydı
var environment = builder.Configuration["Environment"] ?? "dev";
var connectionString = builder.Configuration[$"ConnectionStrings:{environment}"];

builder.Services.AddDbContext<GymContext>(options =>
    options.UseSqlServer(connectionString));
```

🔧 ADIM 2: GymContext Constructor Güncelleme
- DataAccess/Concrete/EntityFramework/GymContext.cs dosyasını aç
- DbContextOptions<GymContext> parametresi alan constructor ekle
- Mevcut OnConfiguring metodunu koşullu yap

GÜNCELLENECEK KOD:
```csharp
public class GymContext : DbContext
{
    public GymContext() { } // Parameterless constructor (backward compatibility)
    
    public GymContext(DbContextOptions<GymContext> options) : base(options) { } // DI constructor
    
    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        // Sadece options set edilmemişse çalışsın
        if (!optionsBuilder.IsConfigured)
        {
            // Mevcut configuration logic
        }
    }
}
```

🔧 ADIM 3: Base Sınıfları Güncelle
A) Core/DataAccess/EntityFramework/EfEntityRepositoryBase.cs:
- Constructor'a TContext injection ekle
- "using (TContext context = new TContext())" pattern'lerini kaldır
- _context field kullan

B) Core/DataAccess/EntityFramework/EfCompanyEntityRepositoryBase.cs:
- Constructor'a TContext injection ekle
- Base class constructor'ını güncelle

🔧 ADIM 4: Build Test
- Projeyi build et
- DI registration doğru mu kontrol et
- DbContext injection çalışıyor mu test et

BEKLENEN SONUÇ:
✅ Build SUCCESS
✅ DbContext DI container'da kayıtlı
✅ Base sınıflar injection'a hazır

BAŞARI KRİTERİ:
- Build hatası yok
- DbContext DI kaydı aktif
- Base sınıflar güncellenmiş

Bu prompt tamamlandıktan sonra "✅ PROMPT 2 TAMAMLANDI - PROMPT 3'E GEÇİLEBİLİR" yaz.

=== PROMPT 2 SONU ===

AI, yukarıdaki talimatları takip et. Sadece DbContext DI kurulumunu yap!
