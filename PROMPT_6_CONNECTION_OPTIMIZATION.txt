🚨 PROMPT 6: CONNECTION POOLING OPTİMİZASYONU 🚨

GÖREV: Connection string'leri 1000+ salon için optimize et

MEVCUT SORUN:
- Max Pool Size=100 (dev), 300 (canlı) - YETERSİZ
- 1000 salon + 100,000 kullanıcı için optimize edilmeli
- Connection timeout ayarları yetersiz

YAPILACAKLAR:

🔧 ADIM 1: Connection String Analizi
- WebAPI/appsettings.json dosyasını aç
- Mevcut connection string parametrelerini analiz et
- Hangi parametrelerin eklenmesi gerektiğini belirle

🔧 ADIM 2: Connection String Optimizasyonu
Aşağıdaki parametreleri ekle/güncelle:

DEV ENVIRONMENT:
```
"dev": "Server=localhost;Database=GymProject;Trusted_Connection=true;Encrypt=False;Max Pool Size=200;Min Pool Size=10;Connection Lifetime=300;Command Timeout=60;Pooling=true;Connection Timeout=30;"
```

STAGING ENVIRONMENT:
```
"staging": "Server=localhost;User Id=sa;Password=************;Database=Staging;Trusted_Connection=false;Encrypt=False;Max Pool Size=400;Min Pool Size=20;Connection Lifetime=300;Command Timeout=60;Pooling=true;Connection Timeout=30;"
```

CANLI ENVIRONMENT:
```
"canlı": "Server=localhost;User Id=sa;Password=************;Database=GymProject;Trusted_Connection=false;Encrypt=False;Max Pool Size=600;Min Pool Size=30;Connection Lifetime=300;Command Timeout=60;Pooling=true;Connection Timeout=30;"
```

🔧 ADIM 3: Parametre Açıklamaları
- Max Pool Size: Maksimum connection sayısı (dev:200, staging:400, canlı:600)
- Min Pool Size: Minimum connection sayısı (dev:10, staging:20, canlı:30)
- Connection Lifetime: Connection yaşam süresi (300 saniye)
- Command Timeout: SQL command timeout (60 saniye)
- Connection Timeout: Bağlantı kurma timeout (30 saniye)
- Pooling: Connection pooling aktif

🔧 ADIM 4: Build Test
- Projeyi build et
- Connection string'lerin doğru parse edildiğini kontrol et
- Database bağlantısının çalıştığını test et

🔧 ADIM 5: Performance Test
- Basit bir database query çalıştır
- Connection pool'un çalıştığını doğrula
- Memory usage'ı kontrol et

BEKLENEN SONUÇ:
✅ Connection pooling optimize edildi
✅ 1000+ salon için hazır
✅ Build SUCCESS
✅ Database bağlantısı çalışıyor

BAŞARI KRİTERİ:
- Connection string'ler güncellenmiş
- Pool size'lar artırılmış
- Build başarılı
- Database bağlantısı aktif

Bu prompt tamamlandıktan sonra "✅ PROMPT 6 TAMAMLANDI - CONNECTION POOLING OPTİMİZE EDİLDİ - PROMPT 7'YE GEÇİLEBİLİR" yaz.

=== PROMPT 6 SONU ===

AI, yukarıdaki talimatları takip et. Connection pooling'i optimize et!
