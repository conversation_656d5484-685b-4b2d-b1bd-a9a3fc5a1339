🚨 KRİTİK SCALABILITY ANALİZİ VE ÇÖZÜM PLANI 🚨

MEVCUT DURUM ANALİZİ:
✅ 1 spor salonu, ~100 üye - SORUNSUZ ÇALIŞIYOR
🎯 HEDEF: 1000+ salon, 100,000+ üye - TEK VERİTABANI

⚠️ TESPİT EDİLEN KRİTİK SORUNLAR:

1. 🔴 DEPENDENCY INJECTION LIFETIME SORUNU (YÜKSEK RİSK)
   - AutofacBusinessModule'de TÜM DAL sınıfları SingleInstance()
   - Bu 100,000 kullanıcıda MEMORY LEAK'e neden olacak
   - DAL sınıfları stateful olduğu için thread-safety sorunu

2. 🔴 DbContext LIFETIME YÖNETİMİ SORUNU (YÜKSEK RİSK)
   - Her metodda "using (var context = new GymContext())" 
   - Connection pool tükenme riski
   - DbContext creation overhead'i çok yüksek

3. 🟡 CONNECTION POOLING OPTİMİZASYONU (ORTA RİSK)
   - Mevcut: Max Pool Size=100 (dev), 300 (canlı)
   - 1000 salon için YETERSİZ
   - Connection timeout ayarları optimize edilmeli

4. 🟡 DATABASE INDEX EKSİKLİKLERİ (ORTA RİSK)
   - CompanyID bazlı indexler eksik
   - Composite indexler optimize edilmeli
   - Query performance sorunları olacak

5. 🟢 CACHE STRATEJİSİ (İYİ DURUM)
   - Multi-tenant cache mevcut
   - Performance monitoring var
   - Sadece fine-tuning gerekli

ÇÖZÜM PLANI (AŞAMALI UYGULAMA):

AŞAMA 1: KRİTİK DI LIFETIME DÜZELTMESİ
- AutofacBusinessModule'de DAL sınıflarını InstancePerLifetimeScope() yap
- Manager sınıflarını InstancePerLifetimeScope() yap
- Sadece stateless sınıfları SingleInstance() bırak

AŞAMA 2: DbContext LIFETIME OPTİMİZASYONU
- DbContext'i DI container'a Scoped olarak kaydet
- "using new GymContext()" pattern'ini kaldır
- Constructor injection ile DbContext kullan

AŞAMA 3: CONNECTION POOLING OPTİMİZASYONU
- Max Pool Size: 500-1000 arası
- Min Pool Size: 50
- Connection Lifetime: 300 saniye
- Command Timeout: 60 saniye

AŞAMA 4: DATABASE INDEX OPTİMİZASYONU
- Tüm tablolara CompanyID composite indexleri
- Query execution plan analizi
- Slow query monitoring

AŞAMA 5: PERFORMANCE MONİTORİNG
- Application Insights entegrasyonu
- Real-time performance metrics
- Automated alerting

DETAYLI UYGULAMA TALİMATLARI:

Bu prompt'u AI'ya gönderdiğinde şu adımları SIRASI İLE otomatik olarak yapacak:

🔧 ADIM 1: DI LIFETIME DÜZELTMESİ
- Business/DependencyResolvers/Autofac/AutofacBusinessModule.cs dosyasını aç
- Tüm .SingleInstance() kayıtlarını .InstancePerLifetimeScope() olarak değiştir
- SADECE şunları SingleInstance() bırak: ITokenHelper, ILogService, ICacheManager
- Build test et, hata varsa düzelt

🔧 ADIM 2: DbContext DI KAYDI
- WebAPI/Program.cs dosyasını aç
- builder.Services.AddDbContext<GymContext>(options => options.UseSqlServer(...)) ekle
- GymContext constructor'ını DbContextOptions kabul edecek şekilde değiştir
- Build test et, hata varsa düzelt

🔧 ADIM 3: DAL SINIFLARINDA DbContext INJECTION (KRİTİK - 31 DOSYA)
⚠️ Bu adım çok kritik! Her dosya dikkatli şekilde refactor edilmeli!

📋 REFACTOR EDİLECEK DAL DOSYALARI (31 ADET):
1. EfCityDal.cs
2. EfCompanyAdressDal.cs
3. EfCompanyDal.cs
4. EfCompanyExerciseDal.cs
5. EfCompanyUserDal.cs
6. EfDebtPaymentDal.cs
7. EfEntryExitHistoryDal.cs
8. EfExerciseCategoryDal.cs
9. EfExpenseDal.cs
10. EfLicensePackageDal.cs
11. EfLicenseTransactionDal.cs
12. EfMemberDal.cs ⚠️ (EN KARMAŞIK - 800+ satır)
13. EfMemberWorkoutProgramDal.cs
14. EfMembershipDal.cs
15. EfMembershipFreezeHistoryDal.cs
16. EfMembershipTypeDal.cs
17. EfOperationClaimDal.cs
18. EfPaymentDal.cs
19. EfProductDal.cs
20. EfRemainingDebtDal.cs
21. EfSystemExerciseDal.cs
22. EfTownDal.cs
23. EfTransactionDal.cs
24. EfUserCompanyDal.cs
25. EfUserDal.cs
26. EfUserDeviceDal.cs
27. EfUserLicenseDal.cs
28. EfUserOperationClaimDal.cs
29. EfWorkoutProgramDayDal.cs
30. EfWorkoutProgramExerciseDal.cs
31. EfWorkoutProgramTemplateDal.cs

🔧 HER DOSYA İÇİN YAPILACAKLAR:
A) Constructor'a DbContext injection ekle
B) _context private field ekle
C) Tüm "using (GymContext context = new GymContext())" pattern'lerini kaldır
D) _context kullanacak şekilde refactor et
E) IDisposable pattern'i kaldır (DbContext artık DI tarafından yönetiliyor)
F) Her dosyayı ayrı ayrı build test et

🚨 ÖZEL DİKKAT GEREKTİREN DOSYALAR:
- EfMemberDal.cs: 800+ satır, 20+ metod, karmaşık LINQ query'ler
- EfPaymentDal.cs: Financial işlemler, transaction kritik
- EfMembershipDal.cs: Business logic heavy
- EfEntryExitHistoryDal.cs: High volume data

📝 REFACTOR PATTERN ÖRNEĞİ:
ÖNCE:
```csharp
public class EfMemberDal : EfCompanyEntityRepositoryBase<Member, GymContext>, IMemberDal
{
    private readonly ICompanyContext _companyContext;

    public EfMemberDal(ICompanyContext companyContext) : base(companyContext)
    {
        _companyContext = companyContext;
    }

    public List<MemberDto> GetMembers()
    {
        using (GymContext context = new GymContext()) // ❌ KALDIRILACAK
        {
            // query logic
        }
    }
}
```

SONRA:
```csharp
public class EfMemberDal : EfCompanyEntityRepositoryBase<Member, GymContext>, IMemberDal
{
    private readonly ICompanyContext _companyContext;
    private readonly GymContext _context; // ✅ YENİ FIELD

    public EfMemberDal(ICompanyContext companyContext, GymContext context) : base(companyContext)
    {
        _companyContext = companyContext;
        _context = context; // ✅ INJECTION
    }

    public List<MemberDto> GetMembers()
    {
        // using kaldırıldı, direkt _context kullanılıyor ✅
        // query logic
    }
}
```

⚠️ HER DOSYAYI TEK TEK İŞLE, ATLAMA!

🔧 ADIM 3.1: BASE SINIFLAR REFACTOR (ÖNCE BUNLAR)
1. Core/DataAccess/EntityFramework/EfEntityRepositoryBase.cs
   - Constructor'a DbContext injection ekle
   - "using (TContext context = new TContext())" kaldır
   - _context field kullan

2. Core/DataAccess/EntityFramework/EfCompanyEntityRepositoryBase.cs
   - Constructor'a DbContext injection ekle
   - Base class'ı güncelle
   - "using (TContext context = new TContext())" kaldır

🔧 ADIM 3.2: DAL SINIFLAR REFACTOR (BASE'DEN SONRA)
- Yukarıdaki 31 dosyayı sırasıyla refactor et
- Her dosyada build test yap
- Hata çıkarsa düzelt, sonraki dosyaya geç

🔧 ADIM 4: CONNECTION STRING OPTİMİZASYONU
- WebAPI/appsettings.json dosyasını aç
- Connection string'lere şu parametreleri ekle:
  * Max Pool Size=500
  * Min Pool Size=50
  * Connection Lifetime=300
  * Command Timeout=60
  * Pooling=true
- Build test et, hata varsa düzelt

🔧 ADIM 5: DATABASE INDEX MİGRATİON
- Yeni migration dosyası oluştur: ScalabilityIndexesMigration.sql
- Tüm tablolar için CompanyID composite indexleri ekle
- Performance critical query'ler için özel indexler ekle
- Migration script'ini test et

🔧 ADIM 6: PERFORMANCE MONİTORİNG GÜÇLENDİRME
- PerformanceAspect'i güçlendir
- Memory usage monitoring ekle
- Connection pool monitoring ekle
- Cache hit ratio monitoring ekle
- Build test et, hata varsa düzelt

🔧 ADIM 7: FINAL TEST VE RAPOR
- Tüm projeyi build et
- Unit test'leri çalıştır
- Performance test senaryoları oluştur
- Memory usage karşılaştırması yap
- Connection pool efficiency ölç
- Kapsamlı test raporu çıkar

🚨 HATA YÖNETİMİ TALİMATLARI:
1. Build hatası çıkarsa:
   - Hatayı analiz et
   - Missing using statements ekle
   - Constructor signature'ları düzelt
   - Dependency injection kayıtlarını kontrol et

2. Runtime hatası çıkarsa:
   - DbContext lifetime scope'unu kontrol et
   - Circular dependency var mı kontrol et
   - Connection string doğru mu kontrol et

3. Performance sorunu çıkarsa:
   - Connection pool ayarlarını kontrol et
   - Query execution plan'ları analiz et
   - Cache hit ratio'larını kontrol et

📊 BAŞARI METRİKLERİ:
- Build: ✅ SUCCESS (hata yok)
- Memory Usage: ⬇️ %70 azalma bekleniyor
- DB Connections: ⬇️ %80 azalma bekleniyor
- Query Performance: ⬆️ %500 iyileşme bekleniyor
- Scalability: ✅ 1000+ salon ready

BEKLENEN SONUÇLAR:
- Memory usage %70 azalma
- Database connection efficiency %300 artış
- Query performance %500 iyileşme
- 1000+ salon desteği
- 100,000+ kullanıcı kapasitesi

⚠️ UYARI: Bu değişiklikler BREAKING CHANGES içerir. Staging ortamında test edilmeli.

🎯 BAŞARI KRİTERLERİ:
- Memory leak testleri PASS
- Load test 10,000 concurrent user PASS
- Database stress test PASS
- 24 saat uptime test PASS

📋 AI İÇİN ÖZEL TALİMATLAR:

1. Her adımı tamamladıktan sonra "✅ ADIM X TAMAMLANDI" yaz
2. Build hatası çıkarsa, hatayı analiz et ve düzelt
3. Değişiklik yapmadan önce mevcut kodu analiz et
4. Backup almaya gerek yok, git history var
5. Test sonuçlarını detaylı raporla
6. Performance metrikleri karşılaştır (önce/sonra)
7. Memory usage değişimlerini ölç
8. Connection pool efficiency'yi hesapla

🚨 UYARI: Bu işlem PRODUCTION READY değişiklikler içerir!

Bu prompt'u AI'ya gönder, tüm scalability sorunlarını otomatik olarak çözsün! 🚀

=== PROMPT SONU ===

AI, yukarıdaki talimatları takip ederek sistemin scalability sorunlarını çöz. Her adımı sırasıyla uygula, test et ve raporla. Başla!
