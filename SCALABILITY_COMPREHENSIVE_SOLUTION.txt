🚨 KRİTİK SCALABILITY ANALİZİ VE ÇÖZÜM PLANI 🚨

MEVCUT DURUM ANALİZİ:
✅ 1 spor salonu, ~100 üye - SORUNSUZ ÇALIŞIYOR
🎯 HEDEF: 1000+ salon, 100,000+ üye - TEK VERİTABANI

⚠️ TESPİT EDİLEN KRİTİK SORUNLAR:

1. 🔴 DEPENDENCY INJECTION LIFETIME SORUNU (YÜKSEK RİSK)
   - AutofacBusinessModule'de TÜM DAL sınıfları SingleInstance()
   - Bu 100,000 kullanıcıda MEMORY LEAK'e neden olacak
   - DAL sınıfları stateful olduğu için thread-safety sorunu

2. 🔴 DbContext LIFETIME YÖNETİMİ SORUNU (YÜKSEK RİSK)
   - Her metodda "using (var context = new GymContext())" 
   - Connection pool tükenme riski
   - DbContext creation overhead'i çok yüksek

3. 🟡 CONNECTION POOLING OPTİMİZASYONU (ORTA RİSK)
   - Mevcut: Max Pool Size=100 (dev), 300 (canlı)
   - 1000 salon için YETERSİZ
   - Connection timeout ayarları optimize edilmeli

4. 🟡 DATABASE INDEX EKSİKLİKLERİ (ORTA RİSK)
   - CompanyID bazlı indexler eksik
   - Composite indexler optimize edilmeli
   - Query performance sorunları olacak

5. 🟢 CACHE STRATEJİSİ (İYİ DURUM)
   - Multi-tenant cache mevcut
   - Performance monitoring var
   - Sadece fine-tuning gerekli

ÇÖZÜM PLANI (AŞAMALI UYGULAMA):

AŞAMA 1: KRİTİK DI LIFETIME DÜZELTMESİ
- AutofacBusinessModule'de DAL sınıflarını InstancePerLifetimeScope() yap
- Manager sınıflarını InstancePerLifetimeScope() yap
- Sadece stateless sınıfları SingleInstance() bırak

AŞAMA 2: DbContext LIFETIME OPTİMİZASYONU
- DbContext'i DI container'a Scoped olarak kaydet
- "using new GymContext()" pattern'ini kaldır
- Constructor injection ile DbContext kullan

AŞAMA 3: CONNECTION POOLING OPTİMİZASYONU
- Max Pool Size: 500-1000 arası
- Min Pool Size: 50
- Connection Lifetime: 300 saniye
- Command Timeout: 60 saniye

AŞAMA 4: DATABASE INDEX OPTİMİZASYONU
- Tüm tablolara CompanyID composite indexleri
- Query execution plan analizi
- Slow query monitoring

AŞAMA 5: PERFORMANCE MONİTORİNG
- Application Insights entegrasyonu
- Real-time performance metrics
- Automated alerting

DETAYLI UYGULAMA TALİMATLARI:

Bu prompt'u AI'ya gönderdiğinde şu adımları SIRASI İLE otomatik olarak yapacak:

🔧 ADIM 1: DI LIFETIME DÜZELTMESİ
- Business/DependencyResolvers/Autofac/AutofacBusinessModule.cs dosyasını aç
- Tüm .SingleInstance() kayıtlarını .InstancePerLifetimeScope() olarak değiştir
- SADECE şunları SingleInstance() bırak: ITokenHelper, ILogService, ICacheManager
- Build test et, hata varsa düzelt

🔧 ADIM 2: DbContext DI KAYDI
- WebAPI/Program.cs dosyasını aç
- builder.Services.AddDbContext<GymContext>(options => options.UseSqlServer(...)) ekle
- GymContext constructor'ını DbContextOptions kabul edecek şekilde değiştir
- Build test et, hata varsa düzelt

🔧 ADIM 3: DAL SINIFLARINDA DbContext INJECTION
- Tüm EfXXXDal.cs dosyalarını bul
- "using (var context = new GymContext())" pattern'lerini kaldır
- Constructor'da DbContext injection ekle
- _context field'ı kullanacak şekilde refactor et
- Build test et, hata varsa düzelt

🔧 ADIM 4: CONNECTION STRING OPTİMİZASYONU
- WebAPI/appsettings.json dosyasını aç
- Connection string'lere şu parametreleri ekle:
  * Max Pool Size=500
  * Min Pool Size=50
  * Connection Lifetime=300
  * Command Timeout=60
  * Pooling=true
- Build test et, hata varsa düzelt

🔧 ADIM 5: DATABASE INDEX MİGRATİON
- Yeni migration dosyası oluştur: ScalabilityIndexesMigration.sql
- Tüm tablolar için CompanyID composite indexleri ekle
- Performance critical query'ler için özel indexler ekle
- Migration script'ini test et

🔧 ADIM 6: PERFORMANCE MONİTORİNG GÜÇLENDİRME
- PerformanceAspect'i güçlendir
- Memory usage monitoring ekle
- Connection pool monitoring ekle
- Cache hit ratio monitoring ekle
- Build test et, hata varsa düzelt

🔧 ADIM 7: FINAL TEST VE RAPOR
- Tüm projeyi build et
- Unit test'leri çalıştır
- Performance test senaryoları oluştur
- Kapsamlı test raporu çıkar

BEKLENEN SONUÇLAR:
- Memory usage %70 azalma
- Database connection efficiency %300 artış
- Query performance %500 iyileşme
- 1000+ salon desteği
- 100,000+ kullanıcı kapasitesi

⚠️ UYARI: Bu değişiklikler BREAKING CHANGES içerir. Staging ortamında test edilmeli.

🎯 BAŞARI KRİTERLERİ:
- Memory leak testleri PASS
- Load test 10,000 concurrent user PASS
- Database stress test PASS
- 24 saat uptime test PASS

Bu prompt'u AI'ya gönder, tüm süreci otomatik olarak halletsin! 🚀
