🚨 PROMPT 5: DAL REFACTOR - BATCH 3 (SON 11 DOSYA) 🚨

GÖREV: Son 11 DAL dosyasını DbContext injection'a geçir

⚠️ Bu son batch! Tüm DAL refactor'ı tamamlanacak.

REFACTOR EDİLECEK DOSYALAR (BATCH 3 - SON):
21. EfSystemExerciseDal.cs
22. EfTownDal.cs
23. EfTransactionDal.cs
24. EfUserCompanyDal.cs
25. EfUserDal.cs
26. EfUserDeviceDal.cs
27. EfUserLicenseDal.cs
28. EfUserOperationClaimDal.cs
29. EfWorkoutProgramDayDal.cs
30. EfWorkoutProgramExerciseDal.cs
31. EfWorkoutProgramTemplateDal.cs

🔧 HER DOSYA İÇİN YAPILACAKLAR:

A) Constructor'a GymContext injection ekle
B) Tüm "using (GymContext context = new GymContext())" pattern'lerini kaldır
C) _context kullanacak şekilde refactor et

🔧 REFACTOR SIRASI:
21. EfSystemExerciseDal.cs → Build test
22. EfTownDal.cs → Build test
23. EfTransactionDal.cs → Build test
24. EfUserCompanyDal.cs → Build test
25. EfUserDal.cs → Build test
26. EfUserDeviceDal.cs → Build test
27. EfUserLicenseDal.cs → Build test
28. EfUserOperationClaimDal.cs → Build test
29. EfWorkoutProgramDayDal.cs → Build test
30. EfWorkoutProgramExerciseDal.cs → Build test
31. EfWorkoutProgramTemplateDal.cs → Build test

🔧 FINAL KONTROL:
- Tüm 31 DAL dosyası refactor edildi mi?
- Hiçbir "using (GymContext context = new GymContext())" kalmadı mı?
- Tüm constructor'larda GymContext injection var mı?

🚨 ÖNEMLİ KURALLAR:
- Her dosyayı ayrı ayrı işle
- Her dosyadan sonra build test yap
- Son dosyadan sonra full build test yap
- Pattern tutarlılığını koru

BEKLENEN SONUÇ:
✅ 11 dosya başarıyla refactor edildi
✅ Toplam 31 DAL dosyası tamamlandı
✅ Build SUCCESS
✅ DbContext injection tamamen aktif

BAŞARI KRİTERİ:
- 31 DAL dosyası tamamlandı
- Using pattern'leri tamamen kaldırıldı
- Build başarılı

Bu prompt tamamlandıktan sonra "✅ PROMPT 5 TAMAMLANDI - TÜM DAL REFACTOR BİTTİ (31/31) - PROMPT 6'YA GEÇİLEBİLİR" yaz.

=== PROMPT 5 SONU ===

AI, yukarıdaki talimatları takip et. Son 11 DAL dosyasını refactor et ve DAL refactor'ını tamamla!
