🚨 PROMPT 3: DAL REFACTOR - BATCH 1 (İLK 10 DOSYA) 🚨

GÖREV: İlk 10 DAL dosyasını DbContext injection'a geçir

⚠️ Bu batch'te sadece 10 dosya işlenecek, fazlasına dokunma!

REFACTOR EDİLECEK DOSYALAR (BATCH 1):
1. EfCityDal.cs
2. EfCompanyAdressDal.cs  
3. EfCompanyDal.cs
4. EfCompanyExerciseDal.cs
5. EfCompanyUserDal.cs
6. EfDebtPaymentDal.cs
7. EfEntryExitHistoryDal.cs
8. EfExerciseCategoryDal.cs
9. EfExpenseDal.cs
10. EfLicensePackageDal.cs

🔧 HER DOSYA İÇİN YAPILACAKLAR:

A) Constructor'a GymContext injection ekle:
```csharp
private readonly GymContext _context;

public EfXXXDal(ICompanyContext companyContext, GymContext context) : base(companyContext)
{
    _companyContext = companyContext;
    _context = context;
}
```

B) Tüm "using (GymContext context = new GymContext())" pattern'lerini kaldır

C) _context kullanacak şekilde refactor et:
```csharp
// ÖNCE:
using (GymContext context = new GymContext())
{
    var result = context.Members.Where(...);
}

// SONRA:
var result = _context.Members.Where(...);
```

🔧 REFACTOR SIRASI:
1. EfCityDal.cs → Build test
2. EfCompanyAdressDal.cs → Build test  
3. EfCompanyDal.cs → Build test
4. EfCompanyExerciseDal.cs → Build test
5. EfCompanyUserDal.cs → Build test
6. EfDebtPaymentDal.cs → Build test
7. EfEntryExitHistoryDal.cs → Build test
8. EfExerciseCategoryDal.cs → Build test
9. EfExpenseDal.cs → Build test
10. EfLicensePackageDal.cs → Build test

🚨 ÖNEMLİ KURALLAR:
- Her dosyayı ayrı ayrı işle
- Her dosyadan sonra build test yap
- Hata çıkarsa o dosyayı düzelt, sonrakine geç
- 10 dosyadan fazlasına dokunma
- Pattern tutarlılığını koru

BEKLENEN SONUÇ:
✅ 10 dosya başarıyla refactor edildi
✅ Build SUCCESS
✅ DbContext injection çalışıyor

BAŞARI KRİTERİ:
- 10 dosya tamamlandı
- Build hatası yok
- Using pattern'leri kaldırıldı

Bu prompt tamamlandıktan sonra "✅ PROMPT 3 TAMAMLANDI - BATCH 1 (10 DOSYA) BİTTİ - PROMPT 4'E GEÇİLEBİLİR" yaz.

=== PROMPT 3 SONU ===

AI, yukarıdaki talimatları takip et. Sadece ilk 10 DAL dosyasını refactor et!
