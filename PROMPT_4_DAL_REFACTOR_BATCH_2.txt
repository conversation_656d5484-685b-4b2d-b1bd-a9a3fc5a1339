🚨 PROMPT 4: DAL REFACTOR - BATCH 2 (ORTA 10 DOSYA) 🚨

GÖREV: İkinci 10 DAL dosyasını DbContext injection'a geçir

⚠️ Bu batch'te sadece 10 dosya işlenecek, fazlasına dokunma!

REFACTOR EDİLECEK DOSYALAR (BATCH 2):
11. EfLicenseTransactionDal.cs
12. EfMemberDal.cs ⚠️ (EN KARMAŞIK - 800+ satır - ÖZEL DİKKAT!)
13. EfMemberWorkoutProgramDal.cs
14. EfMembershipDal.cs
15. EfMembershipFreezeHistoryDal.cs
16. EfMembershipTypeDal.cs
17. EfOperationClaimDal.cs
18. EfPaymentDal.cs ⚠️ (Financial kritik)
19. EfProductDal.cs
20. EfRemainingDebtDal.cs

🔧 HER DOSYA İÇİN YAPILACAKLAR:

A) Constructor'a GymContext injection ekle
B) Tüm "using (GymContext context = new GymContext())" pattern'lerini kaldır
C) _context kullanacak şekilde refactor et

🚨 ÖZEL DİKKAT GEREKTİREN DOSYALAR:

📋 EfMemberDal.cs (12. dosya):
- 800+ satır kod
- 20+ metod
- Karmaşık LINQ query'ler
- Pagination logic
- Her metodu dikkatli kontrol et
- Build test'i mutlaka yap

📋 EfPaymentDal.cs (18. dosya):
- Financial işlemler
- Transaction kritik
- Para hesaplamaları
- Özel dikkat gerekli

🔧 REFACTOR SIRASI:
11. EfLicenseTransactionDal.cs → Build test
12. EfMemberDal.cs → ⚠️ ÖZEL DİKKAT → Build test
13. EfMemberWorkoutProgramDal.cs → Build test
14. EfMembershipDal.cs → Build test
15. EfMembershipFreezeHistoryDal.cs → Build test
16. EfMembershipTypeDal.cs → Build test
17. EfOperationClaimDal.cs → Build test
18. EfPaymentDal.cs → ⚠️ ÖZEL DİKKAT → Build test
19. EfProductDal.cs → Build test
20. EfRemainingDebtDal.cs → Build test

🚨 ÖNEMLİ KURALLAR:
- EfMemberDal.cs'ye ekstra zaman ayır
- Her metodu tek tek kontrol et
- Karmaşık query'lerde dikkatli ol
- Her dosyadan sonra build test yap
- 10 dosyadan fazlasına dokunma

BEKLENEN SONUÇ:
✅ 10 dosya başarıyla refactor edildi
✅ EfMemberDal.cs sorunsuz çalışıyor
✅ Build SUCCESS

BAŞARI KRİTERİ:
- 10 dosya tamamlandı
- EfMemberDal.cs hatasız
- Build başarılı

Bu prompt tamamlandıktan sonra "✅ PROMPT 4 TAMAMLANDI - BATCH 2 (10 DOSYA) BİTTİ - EfMemberDal.cs DAHİL - PROMPT 5'E GEÇİLEBİLİR" yaz.

=== PROMPT 4 SONU ===

AI, yukarıdaki talimatları takip et. İkinci 10 DAL dosyasını refactor et!
